<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Blog</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header>
        <h1>My Simple Blog</h1>
        <p class="tagline">Thoughts, ideas, and stories</p>
    </header>

    <main>
        <article class="blog-post">
            <h2>Getting Started with Web Development</h2>
            <div class="post-meta">
                <time datetime="2024-01-15">January 15, 2024</time>
            </div>
            <div class="post-content">
                <p>Web development has become one of the most sought-after skills in today's digital age. Whether you're looking to build personal projects or start a career in tech, understanding the fundamentals of HTML, CSS, and JavaScript is essential.</p>
                <p>In this journey, you'll discover how these three technologies work together to create the interactive websites we use every day. HTML provides the structure, CSS adds the style, and JavaScript brings everything to life with interactivity.</p>
                <p>The best part? You can start learning right now with just a text editor and a web browser. No expensive tools or software required!</p>
            </div>
        </article>

        <article class="blog-post">
            <h2>The Art of Minimalist Design</h2>
            <div class="post-meta">
                <time datetime="2024-01-10">January 10, 2024</time>
            </div>
            <div class="post-content">
                <p>Less is often more when it comes to web design. Minimalist design isn't just about removing elements; it's about being intentional with every choice you make.</p>
                <p>By focusing on essential elements and removing unnecessary clutter, we create experiences that are both beautiful and functional. White space becomes your friend, typography takes center stage, and every color choice has purpose.</p>
                <p>This approach not only creates visually appealing websites but also improves user experience, loading times, and accessibility.</p>
            </div>
        </article>

        <article class="blog-post">
            <h2>Understanding CSS Grid Layout</h2>
            <div class="post-meta">
                <time datetime="2024-01-05">January 5, 2024</time>
            </div>
            <div class="post-content">
                <p>CSS Grid has revolutionized the way we create layouts on the web. Gone are the days of complex float-based layouts and hacky positioning tricks.</p>
                <p>With CSS Grid, you can create two-dimensional layouts that are both flexible and maintainable. Define your grid template, place your items, and let the browser handle the rest.</p>
                <p>Whether you're building a simple blog layout or a complex dashboard, CSS Grid provides the tools you need to create responsive, professional layouts with clean, readable code.</p>
            </div>
        </article>

        <article class="blog-post">
            <h2>JavaScript Best Practices in 2024</h2>
            <div class="post-meta">
                <time datetime="2023-12-28">December 28, 2023</time>
            </div>
            <div class="post-content">
                <p>JavaScript continues to evolve, and staying up-to-date with best practices is crucial for writing maintainable, efficient code.</p>
                <p>Modern JavaScript emphasizes clarity and expressiveness. Use const and let instead of var, embrace arrow functions where appropriate, and leverage destructuring to make your code more readable.</p>
                <p>Don't forget about async/await for handling asynchronous operations, and always consider performance implications when working with large datasets or complex DOM manipulations.</p>
            </div>
        </article>

        <article class="blog-post">
            <h2>Building Your First Portfolio Website</h2>
            <div class="post-meta">
                <time datetime="2023-12-20">December 20, 2023</time>
            </div>
            <div class="post-content">
                <p>A portfolio website is your digital business card. It's often the first impression potential clients or employers will have of your work, so making it count is essential.</p>
                <p>Start with a clear structure: an about section, showcase of your best projects, and easy ways to contact you. Remember, quality over quantity - it's better to showcase three amazing projects than ten mediocre ones.</p>
                <p>Keep the design clean and professional, ensure it's responsive across all devices, and don't forget to optimize for search engines. Your portfolio should load quickly and be accessible to all users.</p>
            </div>
        </article>
    </main>

    <footer>
        <p>&copy; 2024 Simple Blog. All rights reserved.</p>
    </footer>
</body>
</html>