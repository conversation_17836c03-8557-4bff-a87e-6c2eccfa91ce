# Azure OpenAI Multi-Agent Configuration
# Usage: uv run python -m massgen.cli --config azure_openai_multi.yaml "Your question here"

# Multiple agents configuration
agents:
  - id: "azure_gpt4"
    backend:
      type: "azure_openai"
      model: "gpt-4.1"  # Your Azure OpenAI deployment name for GPT-4.1
    system_message: "You are an expert AI assistant powered by Azure OpenAI GPT-4. Focus on providing comprehensive and accurate responses."

  - id: "azure_gpt5"
    backend:
      type: "azure_openai"
      model: "gpt-5-chat"  # Your Azure OpenAI deployment name for GPT-5-chat
    system_message: "You are an expert AI assistant powered by Azure OpenAI GPT-5-chat. Collaborate with other agents to provide the best possible solution."

# Display configuration
ui:
  display_type: "rich_terminal"
  logging_enabled: true
