# MassGen Fast Configuration with Conservative Timeouts
# For cost-conscious users who want to prevent runaway usage

# Conservative timeout settings to prevent excessive token usage
timeout_settings:
  orchestrator_timeout_seconds: 30   # 30 seconds max coordination

agents:
  - id: "fast-gpt-5-1"
    backend:
      type: "openai"
      model: "gpt-5"
      text: 
        verbosity: "medium"
      reasoning:
        effort: "high"
        summary: "auto"
      enable_web_search: true
      enable_code_interpreter: true
    # system_message: "You are a helpful AI assistant with web search and code execution capabilities. For any question involving current events, recent information, or real-time data, ALWAYS use web search first."
  - id: "fast-gpt-5-2"
    backend:
      type: "openai"
      model: "gpt-5"
      text: 
        verbosity: "medium"
      reasoning:
        effort: "high"
        summary: "auto"
      enable_web_search: true
      enable_code_interpreter: true
    # system_message: "You are a helpful AI assistant with web search and code execution capabilities. For any question involving current events, recent information, or real-time data, ALWAYS use web search first."


ui:
  display_type: "rich_terminal"
  logging_enabled: true