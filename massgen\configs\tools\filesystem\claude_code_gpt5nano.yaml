agents:
  - id: "claude_code"
    backend:
      type: "claude_code"
      cwd: "claude_code_paper_search_workspace"
      permission_mode: "bypassPermissions"
      
      allowed_tools:
        - "Read"
        - "Write"
        - "Bash"
        - "LS"
        - "WebSearch"
        
  - id: "gpt-5-nano"
    backend:
      type: "openai"
      model: "gpt-5-nano"
      reasoning:
        effort: "medium"
        # summary: "auto"
      enable_web_search: true
      enable_code_interpreter: true

ui:
  display_type: "rich_terminal"
  logging_enabled: true