repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.3.0
    hooks:
      - id: check-ast
      - id: sort-simple-yaml
      - id: check-yaml
        exclude: |
          (?x)^(
              meta.yaml
          )$
      - id: check-xml
      - id: check-toml
      - id: check-docstring-first
      - id: check-json
      - id: fix-encoding-pragma
      - id: detect-private-key
      - id: trailing-whitespace
  - repo: https://github.com/asottile/add-trailing-comma
    rev: v3.1.0
    hooks:
      - id: add-trailing-comma
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.1.6
    hooks:
      - id: ruff
        args: [--fix, massgen, docs/presentation]
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: [--profile=black, massgen, docs/presentation]
  - repo: https://github.com/PyCQA/autoflake
    rev: v2.2.1
    hooks:
      - id: autoflake
        args: [--in-place, --remove-all-unused-imports, --remove-unused-variables, massgen, docs/presentation]
  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
    - id: black
      args: [--line-length=200, massgen, docs/presentation]
  - repo: https://github.com/PyCQA/flake8
    rev: 6.1.0
    hooks:
      - id: flake8
        args: [--max-line-length=200, --extend-ignore=E203, massgen, docs/presentation]
        exclude: ^docs
  - repo: https://github.com/pylint-dev/pylint
    rev: v3.0.2
    hooks:
      - id: pylint
        exclude:
            (?x)(
                ^docs
                | \.md$
                | \.html$
                | ^examples
          )
        args: [
          --max-line-length=200,
          --disable=W0511,
          --disable=W0718,
          --disable=W0122,
          --disable=C0103,
          --disable=R0913,
          --disable=E0401,
          --disable=E1101,
          --disable=C0415,
          --disable=W0603,
          --disable=R1705,
          --disable=R0914,
          --disable=E0601,
          --disable=W0602,
          --disable=W0604,
          --disable=R0801,
          --disable=R0902,
          --disable=R0903,
          --disable=C0123,
          --disable=W0231,
          --disable=W1113,
          --disable=W0221,
          --disable=R0401,
          --disable=W0632,
          --disable=W0123,
          --disable=C3001,
          massgen/backend
        ]

  # - repo: https://github.com/pre-commit/mirrors-mypy
  #   rev: v1.7.0
  #   hooks:
  #     - id: mypy
  #       exclude:
  #           (?x)(
  #               ^docs
  #               | \.html$
  #           )
  #       args: [ --disallow-untyped-defs,
  #               --disallow-incomplete-defs,
  #               --ignore-missing-imports,
  #               --disable-error-code=var-annotated,
  #               --disable-error-code=union-attr,
  #               --disable-error-code=assignment,
  #               --disable-error-code=attr-defined,
  #               --disable-error-code=import-untyped,
  #               --disable-error-code=truthy-function,
  #               --disable-error-code=typeddict-item,
  #               --follow-imports=skip,
  #               --explicit-package-bases,
  #               massgen/backend
  #               ]
  - repo: https://github.com/regebro/pyroma
    rev: "4.0"
    hooks:
      - id: pyroma
        args: [.]