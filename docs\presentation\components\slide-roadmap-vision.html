        <!-- Slide 23: Roadmap & Vision -->
        <div class="slide">
            <div class="icon">🔮</div>
            <h2>Vision: The Path to Exponential Intelligence</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; align-items: center;">
                <div>
                    <ul style="font-size: 1.6em;">
                        <li><strong>Hurdles:</strong> Shared memory, context, interoperability</li>
                        <li><strong>Roadmap:</strong> More models/agents, web UI</li>
                        <li><strong>Vision:</strong> Recursive agents bootstrapping intelligence</li>
                    </ul>
                </div>
                <div style="text-align: center;">
                    <svg viewBox="0 0 1200 600" xmlns="http://www.w3.org/2000/svg" style="width: 100%; max-width: 500px; height: auto;">
                      <defs>
                        <marker id="arrowhead" markerWidth="8" markerHeight="6" 
                         refX="7" refY="3" orient="auto">
                          <polygon points="0 0, 8 3, 0 6" fill="#333" />
                        </marker>
                        
                        <!-- Gradient definitions -->
                        <linearGradient id="grokGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                          <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
                          <stop offset="100%" style="stop-color:#2E7D32;stop-opacity:1" />
                        </linearGradient>
                        
                        <linearGradient id="geminiGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                          <stop offset="0%" style="stop-color:#2196F3;stop-opacity:1" />
                          <stop offset="100%" style="stop-color:#1565C0;stop-opacity:1" />
                        </linearGradient>
                        
                        <linearGradient id="claudeGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                          <stop offset="0%" style="stop-color:#FF9800;stop-opacity:1" />
                          <stop offset="100%" style="stop-color:#E65100;stop-opacity:1" />
                        </linearGradient>
                        
                        <linearGradient id="massgenGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                          <stop offset="0%" style="stop-color:#9C27B0;stop-opacity:1" />
                          <stop offset="100%" style="stop-color:#4A148C;stop-opacity:1" />
                        </linearGradient>
                      </defs>
                      
                      <!-- Background -->
                      <rect width="1200" height="600" fill="#f8f9fa"/>
                      
                      <!-- Level 1: Individual Agents -->
                      <text x="100" y="100" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#666">
                        Agents
                      </text>
                      
                      <!-- Agent clusters -->
                      <!-- Grok -->
                      <circle cx="140" cy="130" r="20" fill="url(#grokGrad)" stroke="#2E7D32" stroke-width="3"/>
                      <circle cx="180" cy="130" r="20" fill="url(#grokGrad)" stroke="#2E7D32" stroke-width="3"/>
                      <circle cx="220" cy="130" r="20" fill="url(#grokGrad)" stroke-width="3"/>
                      <text x="180" y="170" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#333" font-weight="bold">Grok</text>
                      
                      <!-- Gemini -->
                      <circle cx="280" cy="130" r="20" fill="url(#geminiGrad)" stroke="#1565C0" stroke-width="3"/>
                      <circle cx="320" cy="130" r="20" fill="url(#geminiGrad)" stroke="#1565C0" stroke-width="3"/>
                      <circle cx="360" cy="130" r="20" fill="url(#geminiGrad)" stroke-width="3"/>
                      <text x="320" y="170" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#333" font-weight="bold">Gemini</text>
                      
                      <!-- Claude -->
                      <circle cx="420" cy="130" r="20" fill="url(#claudeGrad)" stroke="#E65100" stroke-width="3"/>
                      <circle cx="460" cy="130" r="20" fill="url(#claudeGrad)" stroke="#E65100" stroke-width="3"/>
                      <circle cx="500" cy="130" r="20" fill="url(#claudeGrad)" stroke-width="3"/>
                      <text x="460" y="170" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#333" font-weight="bold">Claude</text>
                      
                      <!-- GPT -->
                      <circle cx="560" cy="130" r="20" fill="#10B981" stroke="#047857" stroke-width="3"/>
                      <circle cx="600" cy="130" r="20" fill="#10B981" stroke="#047857" stroke-width="3"/>
                      <circle cx="640" cy="130" r="20" fill="#10B981" stroke-width="3"/>
                      <text x="600" y="170" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#333" font-weight="bold">GPT</text>
                      
                      <!-- AG2 -->
                      <circle cx="700" cy="130" r="20" fill="#DC2626" stroke="#991B1B" stroke-width="3"/>
                      <circle cx="740" cy="130" r="20" fill="#DC2626" stroke="#991B1B" stroke-width="3"/>
                      <circle cx="780" cy="130" r="20" fill="#DC2626" stroke-width="3"/>
                      <text x="740" y="170" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#333" font-weight="bold">AG2</text>
                      
                      <!-- Level 2: Systems -->
                      <text x="50" y="220" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#666">
                        Systems
                      </text>
                      
                      <!-- System boxes -->
                      <rect x="60" y="250" width="140" height="70" rx="12" fill="url(#grokGrad)" stroke="#2E7D32" stroke-width="3"/>
                      <text x="130" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white">
                        Grok Heavy
                      </text>
                      
                      <rect x="220" y="250" width="140" height="70" rx="12" fill="url(#geminiGrad)" stroke="#1565C0" stroke-width="3"/>
                      <text x="290" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white">
                        DeepThink
                      </text>
                      
                      <rect x="380" y="250" width="140" height="70" rx="12" fill="url(#claudeGrad)" stroke="#E65100" stroke-width="3"/>
                      <text x="450" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white">
                        Claude Code
                      </text>
                      
                      <rect x="540" y="250" width="140" height="70" rx="12" fill="#10B981" stroke="#047857" stroke-width="3"/>
                      <text x="610" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white">
                        ChatGPT
                      </text>
                      
                      <rect x="700" y="250" width="140" height="70" rx="12" fill="#DC2626" stroke="#991B1B" stroke-width="3"/>
                      <text x="770" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white">
                        AG2
                      </text>
                      
                      <!-- Arrows from agents to systems -->
                      <line x1="180" y1="190" x2="130" y2="240" stroke="#333" stroke-width="4" marker-end="url(#arrowhead)" opacity="0.7"/>
                      <line x1="320" y1="190" x2="290" y2="240" stroke="#333" stroke-width="4" marker-end="url(#arrowhead)" opacity="0.7"/>
                      <line x1="460" y1="190" x2="450" y2="240" stroke="#333" stroke-width="4" marker-end="url(#arrowhead)" opacity="0.7"/>
                      <line x1="600" y1="190" x2="610" y2="240" stroke="#333" stroke-width="4" marker-end="url(#arrowhead)" opacity="0.7"/>
                      <line x1="740" y1="190" x2="770" y2="240" stroke="#333" stroke-width="4" marker-end="url(#arrowhead)" opacity="0.7"/>
                      
                      <!-- Level 3: MassGen -->
                      <text x="100" y="400" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#666">
                        Orchestrator
                      </text>
                      
                      <!-- MassGen -->
                      <rect x="200" y="430" width="500" height="90" rx="20" fill="url(#massgenGrad)" stroke="#4A148C" stroke-width="5"/>
                      <text x="450" y="485" text-anchor="middle" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="white">
                        MassGen
                      </text>
                      
                      <!-- Arrows from systems to MassGen -->
                      <line x1="130" y1="330" x2="300" y2="420" stroke="#333" stroke-width="5" marker-end="url(#arrowhead)"/>
                      <line x1="290" y1="330" x2="380" y2="420" stroke="#333" stroke-width="5" marker-end="url(#arrowhead)"/>
                      <line x1="450" y1="330" x2="450" y2="420" stroke="#333" stroke-width="5" marker-end="url(#arrowhead)"/>
                      <line x1="610" y1="330" x2="520" y2="420" stroke="#333" stroke-width="5" marker-end="url(#arrowhead)"/>
                      <line x1="770" y1="330" x2="600" y2="420" stroke="#333" stroke-width="5" marker-end="url(#arrowhead)"/>
                      
                      <!-- Scaling visualization -->
                      <g transform="translate(1050, 120)">
                        <circle cx="0" cy="0" r="30" fill="#E3F2FD" stroke="#1976D2" stroke-width="4"/>
                        <text x="0" y="10" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" fill="#1976D2" font-weight="bold">1×</text>
                      </g>
                      
                      <g transform="translate(1050, 220)">
                        <circle cx="0" cy="0" r="40" fill="#E8F5E8" stroke="#4CAF50" stroke-width="4"/>
                        <text x="0" y="10" text-anchor="middle" font-family="Arial, sans-serif" font-size="26" fill="#4CAF50" font-weight="bold">10×</text>
                      </g>
                      
                      <g transform="translate(1050, 340)">
                        <circle cx="0" cy="0" r="50" fill="#F3E5F5" stroke="#9C27B0" stroke-width="4"/>
                        <text x="0" y="10" text-anchor="middle" font-family="Arial, sans-serif" font-size="28" fill="#9C27B0" font-weight="bold">100×</text>
                      </g>
                      
                      <!-- Scaling arrows -->
                      <line x1="1050" y1="155" x2="1050" y2="175" stroke="#666" stroke-width="3" marker-end="url(#arrowhead)"/>
                      <line x1="1050" y1="270" x2="1050" y2="290" stroke="#666" stroke-width="3" marker-end="url(#arrowhead)"/>
                      
                      <!-- Challenge callout -->
                      <g transform="translate(820, 430)">
                        <rect x="0" y="0" width="300" height="120" rx="15" fill="#FFEBEE" stroke="#F44336" stroke-width="4"/>
                        <text x="150" y="35" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#C62828">
                          Challenges
                        </text>
                        <text x="150" y="70" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" fill="#333">
                          Consensus
                        </text>
                        <text x="150" y="100" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" fill="#333">
                          Shared Context
                        </text>
                      </g>
                    </svg>
                    <div style="margin-top: 15px; font-size: 1em; color: #7f8c8d; font-style: italic;">
                        The Path to Exponential Intelligence
                    </div>
                </div>
            </div>
        </div>