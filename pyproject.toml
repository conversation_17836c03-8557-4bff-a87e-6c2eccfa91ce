[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "massgen"
dynamic = ["version"]
description = "Multi-Agent Scaling System - A powerful framework for collaborative AI"
readme = { file = "README.md", content-type = "text/markdown" }
requires-python = ">=3.10"
license = { text = "Apache-2.0" }
authors = [
    { name = "MassGen Team", email = "<EMAIL>" }
]
keywords = ["ai", "multi-agent", "collaboration", "orchestration", "llm", "claude", "gemini", "gpt", "xai"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
dependencies = [
    "datasets==3.2.0",
    "openai==1.71.0",
    "xai-sdk==1.0.0",
    "anthropic==0.49.0",
    "nest-asyncio==1.6.0",
    "cerebras-cloud-sdk==1.46.0",
    "lmstudio==1.4.1",
    "wcwidth>=0.2.5",
    "google-genai>=1.27.0",
    "python-dotenv>=1.0.0",
    "PyYAML>=6.0",
    "rich==14.1.0",
    "requests>=2.31.0",
    "typing-extensions>=4.0.0",
    "claude-code-sdk>=0.0.22",
    "loguru>=0.7.0",
    "mcp>=1.12.0",
    "aiohttp>=3.8.0",
    "ruff>=0.13.1",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.3.0",
    "pre-commit>=3.0.0",
    "bandit>=1.7.0",
    "autoflake>=2.1.0",
    "pyupgrade>=3.7.0",
]
docs = [
    "sphinx>=5.0.0",
    "sphinx-rtd-theme>=1.2.0",
    "myst-parser>=1.0.0",
]
all = [
]

[project.scripts]
massgen = "massgen.cli:main"

[project.urls]
Homepage = "https://github.com/Leezekun/MassGen"
Repository = "https://github.com/Leezekun/MassGen"
"Bug Reports" = "https://github.com/Leezekun/MassGen/issues"
Source = "https://github.com/Leezekun/MassGen"
Documentation = "https://github.com/Leezekun/MassGen/blob/main/README.md"

[tool.setuptools]
include-package-data = true
zip-safe = false

[tool.setuptools.dynamic]
version = {attr = "massgen.__version__"}

[tool.setuptools.packages.find]
where = ["."]
include = ["agents*", "massgen*"]
exclude = ["tests*", "docs*", "future_mass*"]

[tool.setuptools.package-data]
massgen = ["examples/*.yaml", "backends/.env.example"]
"*" = ["*.json", "*.yaml", "*.yml", "*.md"]

# Black configuration
[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | future_mass
)/
'''

# isort configuration
[tool.isort]
profile = "black"
line_length = 88
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
skip_glob = ["future_mass/*"]

# flake8 configuration (in setup.cfg since flake8 doesn't support pyproject.toml yet)
# See .flake8 file for configuration

# mypy configuration
[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = false
disallow_incomplete_defs = false
check_untyped_defs = true
disallow_untyped_decorators = false
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = "agents.*"
ignore_errors = true

[[tool.mypy.overrides]]
module = "future_mass.*"
ignore_errors = true

# pytest configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["massgen/tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]
asyncio_mode = "auto"

# Coverage configuration
[tool.coverage.run]
source = ["agents", "massgen_*"]
omit = [
    "*/tests/*",
    "*/test_*",
    "future_mass/*",
    "setup.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

# Bandit configuration
[tool.bandit]
exclude_dirs = ["tests", "future_mass"]
skips = ["B101", "B601"]  # Skip assert_used and shell_injection for test files

[dependency-groups]
dev = [
    "astroid>=3.0.0",
    "autoflake>=2.3.1",
    "isort>=6.0.1",
    "pre-commit>=4.3.0",
    "pylint>=3.0.0",
    "ruff>=0.13.1",
]
