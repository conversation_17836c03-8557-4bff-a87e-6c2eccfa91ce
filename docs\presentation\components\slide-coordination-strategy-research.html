        <!-- Slide 17: Coordination Strategy Research -->
        <div class="slide">
            <div class="icon">🧠</div>
            <h2>Coordination Psychology: How Voting Behavior Changes</h2>
            <div style="text-align: center; margin-bottom: 30px; color: #666;">
                <em>Controlled experiments reveal how coordination mechanisms shape multi-agent behavior</em>
            </div>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px;">
                <div>
                    <h3 style="color: #2c3e50; margin-bottom: 20px; text-align: center;">🎭 Identity Disclosure Effects</h3>
                    
                    <div style="background: #f8f9fa; border-radius: 15px; padding: 20px; margin-bottom: 20px;">
                        <h4 style="color: #3498db; margin: 0 0 15px 0;">Anonymous Voting (Baseline)</h4>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <span style="font-size: 0.9em;">GPT-5 Self-Voting</span>
                            <div style="background: #3498db; height: 20px; width: 81%; border-radius: 10px; position: relative;">
                                <span style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%); color: white; font-size: 0.8em; font-weight: bold;">81.0%</span>
                            </div>
                        </div>
                        <div style="font-size: 0.8em; color: #666; margin-top: 10px;">
                            Agents see: "agent1", "agent2", "agent3", "agent4"
                        </div>
                    </div>
                    
                    <div style="background: #fff3cd; border-radius: 15px; padding: 20px; border-left: 4px solid #f39c12;">
                        <h4 style="color: #856404; margin: 0 0 15px 0;">Identified Voting</h4>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <span style="font-size: 0.9em;">GPT-5 Self-Voting</span>
                            <div style="background: #f39c12; height: 20px; width: 88.4%; border-radius: 10px; position: relative;">
                                <span style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%); color: white; font-size: 0.8em; font-weight: bold;">88.4%</span>
                            </div>
                        </div>
                        <div style="color: #856404; font-size: 0.9em; margin-top: 15px;">
                            <strong>+7.4%</strong> increase in self-voting<br>
                            <strong>Consensus ties:</strong> 14.1% → 23.2%
                        </div>
                        <div style="font-size: 0.8em; color: #666; margin-top: 10px;">
                            Agents see: "GPT-5", "Claude", "Gemini", "Grok"
                        </div>
                    </div>
                </div>
                
                <div>
                    <h3 style="color: #2c3e50; margin-bottom: 20px; text-align: center;">👁️ Vote Visibility Effects</h3>
                    
                    <div style="background: #f8f9fa; border-radius: 15px; padding: 20px; margin-bottom: 20px;">
                        <h4 style="color: #27ae60; margin: 0 0 15px 0;">Hidden Tally (Baseline)</h4>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <span style="font-size: 0.9em;">First-voted Selected</span>
                            <div style="background: #27ae60; height: 20px; width: 54.1%; border-radius: 10px; position: relative;">
                                <span style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%); color: white; font-size: 0.8em; font-weight: bold;">54.1%</span>
                            </div>
                        </div>
                        <div style="font-size: 0.8em; color: #666; margin-top: 10px;">
                            Agents vote independently without seeing others' choices
                        </div>
                    </div>
                    
                    <div style="background: #d4edda; border-radius: 15px; padding: 20px; border-left: 4px solid #27ae60;">
                        <h4 style="color: #155724; margin: 0 0 15px 0;">Visible Tally</h4>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <span style="font-size: 0.9em;">First-voted Selected</span>
                            <div style="background: #155724; height: 20px; width: 67.8%; border-radius: 10px; position: relative;">
                                <span style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%); color: white; font-size: 0.8em; font-weight: bold;">67.8%</span>
                            </div>
                        </div>
                        <div style="color: #155724; font-size: 0.9em; margin-top: 15px;">
                            <strong>+13.7%</strong> herding behavior<br>
                            <strong>Especially strong:</strong> GPT-5 (+40%)
                        </div>
                        <div style="font-size: 0.8em; color: #666; margin-top: 10px;">
                            Agents see ongoing vote counts and reasons
                        </div>
                    </div>
                </div>
            </div>
            
            <div style="margin-top: 30px; background: rgba(116, 185, 255, 0.1); padding: 25px; border-radius: 15px; border: 2px dashed #74b9ff;">
                <h3 style="color: #2c3e50; margin: 0 0 15px 0; text-align: center;">🎯 Key Behavioral Insights</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                    <div style="text-align: center;">
                        <div style="font-size: 1.5em; margin-bottom: 10px;">🎭</div>
                        <strong style="color: #2c3e50;">Identity Matters</strong><br>
                        <span style="color: #666; font-size: 0.9em;">Knowing "who said what" increases bias and reduces consensus</span>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 1.5em; margin-bottom: 10px;">🐑</div>
                        <strong style="color: #2c3e50;">Herding Behavior</strong><br>
                        <span style="color: #666; font-size: 0.9em;">Visible votes create momentum effects, sometimes premature consensus</span>
                    </div>
                </div>
            </div>
        </div>