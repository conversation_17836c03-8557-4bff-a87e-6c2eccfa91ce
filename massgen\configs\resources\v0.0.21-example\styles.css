* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    padding: 3rem 1rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.tagline {
    font-size: 1.1rem;
    opacity: 0.95;
    font-weight: 300;
}

main {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 1rem;
}

.blog-post {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s, box-shadow 0.2s;
}

.blog-post:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.blog-post h2 {
    color: #2c3e50;
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.post-meta {
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e0e0e0;
}

.post-meta time {
    color: #7f8c8d;
    font-size: 0.9rem;
    font-weight: 500;
}

.post-content {
    color: #4a5568;
    line-height: 1.8;
}

.post-content p {
    margin-bottom: 1rem;
}

.post-content p:last-child {
    margin-bottom: 0;
}

footer {
    text-align: center;
    padding: 2rem 1rem;
    margin-top: 3rem;
    background: #2c3e50;
    color: #ecf0f1;
    font-size: 0.9rem;
}

@media (max-width: 600px) {
    header h1 {
        font-size: 2rem;
    }

    .tagline {
        font-size: 1rem;
    }

    .blog-post {
        padding: 1.5rem;
    }

    .blog-post h2 {
        font-size: 1.5rem;
    }
}