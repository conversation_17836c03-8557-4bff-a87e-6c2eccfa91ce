        <!-- Slide 22: Getting Started -->
        <div class="slide">
            <div class="icon">⚡</div>
            <h2>Get Started in 60 Seconds</h2>
            <div class="code-snippet">
                # 1. Clone and setup<br>
                git clone https://github.com/Leezekun/MassGen<br>
                cd MassGen && pip install uv && uv venv<br><br>
                
                # 2. Configure API keys<br>
                cp .env.example .env  # Add your API keys<br><br>
                
                # 3. Run single agent (quick test)<br>
                uv run python -m massgen.cli --model gemini-2.5-flash "When is your knowledge up to"<br><br>
                
                # 4. Run multi-agent collaboration<br>
                uv run python -m massgen.cli --config three_agents_default.yaml "Summarize latest news of github.com/Leezekun/MassGen"
            </div>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 30px;">
                <div style="text-align: center; padding: 20px; background: rgba(46, 204, 113, 0.1); border-radius: 15px;">
                    <h3 style="color: #27ae60; margin-bottom: 15px;">✅ Supported Models & Providers</h3>
                    <div style="font-size: 1em; line-height: 1.5;">
                        <div style="font-weight: bold; margin-bottom: 8px;">🏢 Major Providers:</div>
                        <div>Anthropic Claude & Claude Code • Google Gemini • OpenAI GPT • xAI Grok • ZAI GLM</div>
                        <div style="font-weight: bold; margin: 12px 0 8px 0;">🏠 Local & Extended:</div>
                        <div>Cerebras • Fireworks • Groq • LM Studio • OpenRouter • Together...</div>
                    </div>
                </div>
                <div style="text-align: center; padding: 20px; background: rgba(52, 152, 219, 0.1); border-radius: 15px;">
                    <h3 style="color: #3498db; margin-bottom: 15px;">🛠️ Advanced Tools</h3>
                    <div style="font-size: 1.1em;">Web Search • Code Execution • MCP Tools • File Operations • Browser Automation</div>
                </div>
            </div>
        </div>