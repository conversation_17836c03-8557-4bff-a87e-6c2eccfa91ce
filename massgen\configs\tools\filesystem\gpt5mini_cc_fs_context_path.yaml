# Example configuration demonstrating context_paths filesystem access control
# This config shows how agents can access user-specified files with permission control

agents:
  - id: "agent1"
    backend:
      type: "openai"
      model: "gpt-5"
      text:
        verbosity: "medium"
      reasoning:
        effort: "low"  # Fast responses for debugging
      cwd: "workspace1"

  - id: "agent2"
    backend:
      type: "claude_code"
      cwd: "workspace2"

orchestrator:
    snapshot_storage: "snapshots"
    agent_temporary_workspace: "temp_workspaces"
    # Context paths applied to all agents - read-only access during orchestration, write for final agent if specified
    context_paths:
      - path: "/home/<USER>/GitHubProjects/MassGen/massgen/configs/resources/v0.0.21-example"
        permission: "write"

# UI Configuration
ui:
  type: "rich_terminal"
  logging_enabled: true
