# Dual agent configuration using Notion MCP with Gemini-2.5-pro.
# To avoid connection issues, we give each agent its own Notion token with access to a different page (we call them `LLM Agent Research (1)` and `LLM Agent Research (2)`) and use NOTION_TOKEN_ONE and NOTION_TOKEN_TWO in the .env file to represent them, respectively.
# To set this up, you must do the below twice, since we create one page for each agent (the process for doing it once is detailed in depth [here](https://github.com/makenotion/notion-mcp-server)):
#   1) create a new page titled `LLM Agent Research ({1 or 2})` in Notion.
#   2) generate an integration token (Internal Integration Secret) in Notion by visiting [the integrations page](https://www.notion.so/profile/integrations), and visit the Access tab to add the integration to your new page.
#   3) place it in your .env file: NOTION_TOKEN_{ONE or TWO}="your_notion_token_here"
#
# cmd: uv run python -m massgen.cli --config massgen/configs/gemini_notion_mcp.yaml "Generate and refine a structured Todo list for learning about LLM multi-agent systems, complete with exciting objectives and fun activities. Each time you have a new version, create a new Notion page with a title and the current date and time (including hours, minutes, seconds, and milliseconds) to store the list. Then, verify that you can access the page and read back the content. Create this page as a subpage under an existing notion page called 'LLM Agent Research (x)', where x is either 1 or 2 depending on which you have access to."
agents:
  - id: "gemini-2.5-pro1"
    backend:
      type: "gemini"
      model: "gemini-2.5-pro"
      enable_web_search: true


      # MCP tools configuration
      mcp_servers:
        notionApi:
          type: "stdio"
          command: "npx"
          args: ["-y", "@notionhq/notion-mcp-server"]
          env:
            NOTION_TOKEN: "${NOTION_TOKEN_ONE}" 
      # There are errors with how this tool functions, so we exclude it for now
      exclude_tools:
        - post_search


  - id: "gemini-2.5-pro2"
    backend:
      type: "gemini"
      model: "gemini-2.5-pro"
      enable_web_search: true


      # MCP tools configuration
      mcp_servers:
        notionApi:
          type: "stdio"
          command: "npx"
          args: ["-y", "@notionhq/notion-mcp-server"]
          env:
            NOTION_TOKEN: "${NOTION_TOKEN_TWO}" 
      exclude_tools:
        - post_search


# UI Configuration
ui:
  type: "rich_terminal"
  logging_enabled: true
