        <!-- Slide 11: Context Sharing Challenge -->
        <div class="slide">
            <div class="icon">⚠️</div>
            <h2>The Context Sharing Challenge</h2>
            <div style="display: grid; grid-template-columns: 1fr 1.3fr; gap: 40px; align-items: center;">
                <div>
                    <div style="background: #ffebee; padding: 25px; border-radius: 15px; margin-bottom: 20px; border-left: 4px solid #e74c3c;">
                        <h3 style="margin: 0 0 15px 0; font-size: 1.4em; color: #e74c3c;">❌ Naive Approach 1: Share Answers Only</h3>
                        <ul style="font-size: 1.1em; line-height: 1.6; color: #2c3e50;">
                            <li>Agents only see final text answers</li>
                            <li>Can't verify methodology or data</li>
                            <li>Unable to test or build upon work</li>
                            <li>Lost intermediate context</li>
                        </ul>
                    </div>
                    <div style="background: #fff3cd; padding: 25px; border-radius: 15px; border-left: 4px solid #f39c12;">
                        <h3 style="margin: 0 0 15px 0; font-size: 1.4em; color: #f39c12;">❌ Naive Approach 2: Share Workspace Paths</h3>
                        <ul style="font-size: 1.1em; line-height: 1.6; color: #2c3e50;">
                            <li>Agents interfere with each other's work</li>
                            <li>Data corruption from simultaneous edits</li>
                            <li>Loss of original work context</li>
                            <li>Workspace pollution and conflicts</li>
                        </ul>
                    </div>
                </div>
                <div style="text-align: center;">
                    <svg viewBox="0 0 400 500" xmlns="http://www.w3.org/2000/svg" style="width: 100%; max-width: 500px; height: auto;">
                        <!-- Approach 1: Answer Only -->
                        <text x="200" y="30" text-anchor="middle" font-family="Arial" font-size="16" font-weight="bold" fill="#e74c3c">Approach 1: Answer Only</text>
                        
                        <!-- Agents -->
                        <rect x="40" y="50" width="80" height="40" rx="5" fill="#74b9ff" stroke="#0984e3" stroke-width="2"/>
                        <text x="80" y="75" text-anchor="middle" font-family="Arial" font-size="14" fill="white" font-weight="bold">Agent 1</text>
                        
                        <rect x="280" y="50" width="80" height="40" rx="5" fill="#74b9ff" stroke="#0984e3" stroke-width="2"/>
                        <text x="320" y="75" text-anchor="middle" font-family="Arial" font-size="14" fill="white" font-weight="bold">Agent 2</text>
                        
                        <!-- Answer bubble -->
                        <ellipse cx="200" cy="120" rx="60" ry="25" fill="#ffebee" stroke="#e74c3c" stroke-width="2"/>
                        <text x="200" y="128" text-anchor="middle" font-family="Arial" font-size="12" fill="#e74c3c">"Answer text"</text>
                        
                        <!-- Arrows -->
                        <line x1="120" y1="70" x2="150" y2="105" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead-red)"/>
                        <line x1="280" y1="70" x2="250" y2="105" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead-red)"/>
                        
                        <!-- Problem indicators -->
                        <text x="200" y="170" text-anchor="middle" font-family="Arial" font-size="11" fill="#666">❌ No verification possible</text>
                        
                        <!-- Approach 2: Workspace Sharing -->
                        <text x="200" y="230" text-anchor="middle" font-family="Arial" font-size="16" font-weight="bold" fill="#f39c12">Approach 2: Workspace Sharing</text>
                        
                        <!-- Agents -->
                        <rect x="40" y="260" width="80" height="40" rx="5" fill="#74b9ff" stroke="#0984e3" stroke-width="2"/>
                        <text x="80" y="285" text-anchor="middle" font-family="Arial" font-size="14" fill="white" font-weight="bold">Agent 1</text>
                        
                        <rect x="280" y="260" width="80" height="40" rx="5" fill="#74b9ff" stroke="#0984e3" stroke-width="2"/>
                        <text x="320" y="285" text-anchor="middle" font-family="Arial" font-size="14" fill="white" font-weight="bold">Agent 2</text>
                        
                        <!-- Shared workspace -->
                        <rect x="150" y="320" width="100" height="60" rx="5" fill="#fff3cd" stroke="#f39c12" stroke-width="2"/>
                        <text x="200" y="340" text-anchor="middle" font-family="Arial" font-size="12" fill="#f39c12" font-weight="bold">Shared</text>
                        <text x="200" y="355" text-anchor="middle" font-family="Arial" font-size="12" fill="#f39c12" font-weight="bold">Workspace</text>
                        <text x="200" y="370" text-anchor="middle" font-family="Arial" font-size="10" fill="#f39c12">⚠️ Conflicts!</text>
                        
                        <!-- Arrows -->
                        <line x1="120" y1="280" x2="150" y2="330" stroke="#f39c12" stroke-width="2" marker-end="url(#arrowhead-orange)"/>
                        <line x1="280" y1="280" x2="250" y2="330" stroke="#f39c12" stroke-width="2" marker-end="url(#arrowhead-orange)"/>
                        
                        <!-- Problem indicators -->
                        <text x="200" y="420" text-anchor="middle" font-family="Arial" font-size="11" fill="#666">❌ Data corruption & interference</text>
                        
                        <defs>
                            <marker id="arrowhead-red" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                <polygon points="0 0, 8 3, 0 6" fill="#e74c3c"/>
                            </marker>
                            <marker id="arrowhead-orange" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                <polygon points="0 0, 8 3, 0 6" fill="#f39c12"/>
                            </marker>
                        </defs>
                    </svg>
                </div>
            </div>
            <div style="text-align: center; margin-top: 20px; background: #f8f9fa; padding: 15px; border-radius: 10px;">
                <strong style="font-size: 1.3em; color: #2c3e50;">The Challenge: How to share context without interference?</strong>
            </div>
        </div>