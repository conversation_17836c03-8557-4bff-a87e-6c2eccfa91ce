        <!-- Slide 10: Tech Deep Dive - Binary Decision Framework Solution -->
        <div class="slide">
            <div class="icon">🎯</div>
            <h2>Tech Deep Dive: Binary Decision Framework Solution</h2>
            <div style="display: grid; grid-template-columns: 1fr 1.5fr; gap: 40px; align-items: center;">
                <div>
                    <ul style="font-size: 1.4em;">
                        <li><strong>⚖️ Binary Choice:</strong> Each agent must choose: vote OR new_answer</li>
                        <li><strong>💥 Vote Invalidation:</strong> Any new_answer invalidates ALL existing votes</li>
                        <li><strong>🔄 Reset & Restart:</strong> All agents restart with updated answer context</li>
                        <li><strong>🎭 Anonymous Voting:</strong> Agents see "agent1", "agent2" etc.</li>
                    </ul>
                </div>
                <div style="text-align: center;">
                    <svg viewBox="0 0 500 380" xmlns="http://www.w3.org/2000/svg" style="width: 100%; max-width: 600px; height: auto;">
                        <!-- Round 1 -->
                        <text x="250" y="25" text-anchor="middle" font-family="Arial" font-size="15" font-weight="bold" fill="#2c3e50">Round 1: Agents 1,3,4 vote for Agent 4</text>
                        
                        <!-- Agents with votes -->
                        <rect x="50" y="45" width="80" height="25" rx="3" fill="#00b894" stroke="#00cec9" stroke-width="1"/>
                        <text x="90" y="62" text-anchor="middle" font-family="Arial" font-size="12" fill="white">Vote: agent4</text>
                        
                        <rect x="140" y="45" width="80" height="25" rx="3" fill="#ddd" stroke="#bbb" stroke-width="1"/>
                        <text x="180" y="62" text-anchor="middle" font-family="Arial" font-size="12" fill="#666">No vote yet</text>
                        
                        <rect x="230" y="45" width="80" height="25" rx="3" fill="#00b894" stroke="#00cec9" stroke-width="1"/>
                        <text x="270" y="62" text-anchor="middle" font-family="Arial" font-size="12" fill="white">Vote: agent4</text>
                        
                        <rect x="320" y="45" width="80" height="25" rx="3" fill="#a29bfe" stroke="#6c5ce7" stroke-width="1"/>
                        <text x="360" y="62" text-anchor="middle" font-family="Arial" font-size="12" fill="white">Answer+Vote</text>
                        
                        <!-- Disruption -->
                        <text x="250" y="105" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="#e74c3c">⚡ Agent 2 provides new_answer</text>
                        
                        <!-- Reset arrows -->
                        <path d="M 90 75 L 90 90" stroke="#e74c3c" stroke-width="2" marker-end="url(#x-mark)"/>
                        <path d="M 270 75 L 270 90" stroke="#e74c3c" stroke-width="2" marker-end="url(#x-mark)"/>
                        <path d="M 360 75 L 360 90" stroke="#e74c3c" stroke-width="2" marker-end="url(#x-mark)"/>
                        
                        <!-- Reset mechanism -->
                        <rect x="140" y="120" width="80" height="25" rx="3" fill="#fd79a8" stroke="#e84393" stroke-width="1"/>
                        <text x="180" y="137" text-anchor="middle" font-family="Arial" font-size="12" fill="white">new_answer</text>
                        
                        <!-- Round 2 -->
                        <text x="250" y="175" text-anchor="middle" font-family="Arial" font-size="15" font-weight="bold" fill="#2c3e50">Round 2: All agents restart with 2 answers</text>
                        
                        <!-- New state -->
                        <rect x="50" y="195" width="80" height="25" rx="3" fill="#74b9ff" stroke="#0984e3" stroke-width="1"/>
                        <text x="90" y="212" text-anchor="middle" font-family="Arial" font-size="11" fill="white">restart_pending</text>
                        
                        <rect x="140" y="195" width="80" height="25" rx="3" fill="#a29bfe" stroke="#6c5ce7" stroke-width="1"/>
                        <text x="180" y="212" text-anchor="middle" font-family="Arial" font-size="11" fill="white">Has new answer</text>
                        
                        <rect x="230" y="195" width="80" height="25" rx="3" fill="#74b9ff" stroke="#0984e3" stroke-width="1"/>
                        <text x="270" y="212" text-anchor="middle" font-family="Arial" font-size="11" fill="white">restart_pending</text>
                        
                        <rect x="320" y="195" width="80" height="25" rx="3" fill="#a29bfe" stroke="#6c5ce7" stroke-width="1"/>
                        <text x="360" y="212" text-anchor="middle" font-family="Arial" font-size="11" fill="white">Has old answer</text>
                        
                        <!-- Binary decision -->
                        <text x="250" y="250" text-anchor="middle" font-family="Arial" font-size="13" font-weight="bold" fill="#2c3e50">Each agent decides: vote OR new_answer</text>
                        
                        <!-- Key insight -->
                        <rect x="70" y="265" width="360" height="28" rx="5" fill="#fff3cd" stroke="#f39c12" stroke-width="2"/>
                        <text x="250" y="283" text-anchor="middle" font-family="Arial" font-size="13" font-weight="bold" fill="#856404">🔑 Any new_answer resets ALL votes</text>
                        
                        <!-- Vote invalidation note -->
                        <text x="250" y="315" text-anchor="middle" font-family="Arial" font-size="12" fill="#e74c3c">Votes from Round 1: ❌ INVALID</text>
                        <text x="250" y="335" text-anchor="middle" font-family="Arial" font-size="12" fill="#27ae60">New decisions needed based on 2 available answers</text>
                        
                        <!-- Markers -->
                        <defs>
                            <marker id="x-mark" markerWidth="10" markerHeight="10" refX="5" refY="5" orient="auto">
                                <path d="M 2 2 L 8 8 M 8 2 L 2 8" stroke="#e74c3c" stroke-width="2"/>
                            </marker>
                        </defs>
                    </svg>
                    <div style="margin-top: 15px; font-size: 1em; color: #7f8c8d;">
                        <strong>Key Innovation:</strong> Dynamic equilibrium through vote invalidation
                    </div>
                </div>
            </div>
        </div>