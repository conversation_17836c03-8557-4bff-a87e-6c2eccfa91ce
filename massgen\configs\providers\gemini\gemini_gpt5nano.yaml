# MassGen Three Agent Configuration
# Gemini-2.5-flash, GPT-5-nano, and Claude-3.5-Haiku with builtin tools enabled

agents:
  - id: "gemini2.5flash"
    backend:
      type: "gemini"
      model: "gemini-2.5-flash"
      enable_web_search: true
      # enable_code_execution: true  # Disabled by default - can preempt web search, resulting in weaker search capability
    # system_message: "You are a helpful AI assistant with web search and code execution capabilities. For any question involving current events, recent information, or real-time data, ALWAYS use web search first."

  - id: "gpt-5-nano"
    backend:
      type: "openai"
      model: "gpt-5-nano"
      text:
        verbosity: "medium"
      reasoning:
        effort: "medium"
        summary: "auto"
      enable_web_search: true
      enable_code_interpreter: true
    # system_message: "You are a helpful AI assistant with web search and code execution capabilities. For any question involving current events, recent information, or real-time data, ALWAYS use web search first."

ui:
  display_type: "rich_terminal"
  logging_enabled: true
