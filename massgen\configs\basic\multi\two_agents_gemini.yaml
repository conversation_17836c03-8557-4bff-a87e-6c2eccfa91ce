# cmd: uv run python -m massgen.cli --config massgen/configs/two_agents_gemini.yaml "Generate and refine a structured Todo list for learning about LLM multi-agent systems, complete with exciting objectives and fun activities. Each time you have a new version, create a new Notion page with a title and the current date and time (including hours, minutes, seconds, and milliseconds) to store the list. Then, verify that you can access the page and read back the content. Create this page as a subpage under an existing notion page called 'LLM Agent Research (x)', where x is either 1 or 2 depending on which you have access to."
agents:
  - id: "gemini-2.5-pro1"
    backend:
      type: "gemini"
      model: "gemini-2.5-pro"
      enable_web_search: true

  - id: "gemini-2.5-pro2"
    backend:
      type: "gemini"
      model: "gemini-2.5-pro"
      enable_web_search: true


# UI Configuration
ui:
  type: "rich_terminal"
  logging_enabled: true
