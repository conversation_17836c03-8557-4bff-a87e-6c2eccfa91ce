agent:
  id: "claude_code_twitter_mcp"
  backend:
    type: "claude_code"
    cwd: "claude_code_workspace_twitter_mcp"
    permission_mode: "bypassPermissions"
    
    # Twitter MCP server - using EnesCinr/twitter-mcp
    mcp_servers:
      twitter:
        type: "stdio"
        command: "npx"
        args: ["-y", "@enescinar/twitter-mcp"]
        env:
        # You need to get these from Twitter Developer Portal: https://developer.twitter.com/
        # Required credentials:
          API_KEY: "YOUR_API_KEY"
          API_SECRET_KEY: "YOUR_API_SECRET_KEY"
          ACCESS_TOKEN: "YOUR_ACCESS_TOKEN"
          ACCESS_TOKEN_SECRET: "YOUR_ACCESS_TOKEN_SECRET"

    allowed_tools:
      - "Read"
      - "Write"
      - "Bash"
      - "LS"
      - "WebSearch"
      # MCP tools will be auto-discovered from the server

ui:
  display_type: "rich_terminal"
  logging_enabled: true